import { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { useAgentsApi, Agent, AgentFilters } from 'src/services/api/use-agents-api';
import { useCategoriesApi } from 'src/services/api/use-categories-api';
import { useChatApi } from 'src/services/api/use-chat-api';
import { paths } from 'src/routes/paths';

// Filter options
const TYPE_FILTERS = ['All', 'SINGLE', 'TEAM'];

export const useAgentsView = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredAgents, setFilteredAgents] = useState<Agent[]>([]);
  const { useCreateAgents } = useAgentsApi();
  const { useCreateChat } = useChatApi();
  const navigate = useNavigate();
  const [isCreating, setIsCreating] = useState(false);
  const { mutate: createChat, isPending: isPendingCreateChat } = useCreateChat();

  // Filter states
  const [selectedTypeFilter, setSelectedTypeFilter] = useState(0);
  const [selectedCategoryFilter, setSelectedCategoryFilter] = useState(0);

  // Use the categories API hook to fetch categories (same as templates)
  const { useGetCategories } = useCategoriesApi();
  const { data: categoriesData, isLoading: categoriesLoading } = useGetCategories();

  // Create dynamic category filters with useMemo to prevent infinite re-renders (same as templates)
  const CATEGORY_FILTERS = useMemo(() => {
    console.log('categoriesData', categoriesData);

    if (!categoriesData || !categoriesData.categories || !Array.isArray(categoriesData.categories)) {
      return ['All'];
    }
    return ['All', ...categoriesData.categories.map(category => category.name)];
  }, [categoriesData]);

  // Debug logging (same as templates)
  useEffect(() => {
    if (categoriesData) {
      console.log('Categories loaded:', categoriesData);
      console.log('Category filters:', CATEGORY_FILTERS);
    }
  }, [categoriesData, CATEGORY_FILTERS]);

  // Build filters for API call (same logic as templates)
  const apiFilters: AgentFilters = {
    take: 15,
    skip: 0,
    ...(selectedTypeFilter !== 0 && { type: TYPE_FILTERS[selectedTypeFilter] }),
    ...(selectedCategoryFilter !== 0 && {
      categoryId: categoriesData?.categories?.[selectedCategoryFilter - 1]?.id ?
        parseInt(categoriesData.categories[selectedCategoryFilter - 1].id, 10) : undefined,
    }),
  };

  // Debug logging for API filters (same as templates)
  useEffect(() => {
    console.log('API Filters:', apiFilters);
  }, [apiFilters]);

  // Use the agents API hook to fetch data with filters
  const { useGetAgentss } = useAgentsApi();
  const { data: agentsResponse, isLoading, error, refetch } = useGetAgentss(apiFilters);

  // Extract agents from the response
  const agents = agentsResponse?.agents || [];

  // Apply search filter (client-side for search, server-side for category/type filters)
  useEffect(() => {
    let filtered = agents;

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(
        (agent) =>
          agent.template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          agent.template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
          agent.template.category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          agent.specialRequest.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredAgents(filtered);
  }, [agents, searchQuery]);

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };
  const handleOpenAgent = async (obj: Agent) => {
    if (isCreating) return;

    setIsCreating(true);
    const body = {
      title: 'new chat',
      agentId: obj.id || '',
    };
    createChat(body, {
      onSuccess: (resChat) => {
        const newChatId = resChat?.data?.id;
        navigate(paths.dashboard.agents.chat(obj.template.id, obj.id, newChatId), {
          state: { name: obj.template.name },
        });
        setIsCreating(false);
      },
      onError: () => {
        setIsCreating(false);
      },
    });
  };
  // Handle filter changes
  const handleTypeFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedTypeFilter(newValue);
  };

  const handleCategoryFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedCategoryFilter(newValue);
  };

  return {
    // Data
    agents,
    filteredAgents,

    // Loading states
    isCreating,
    isLoading,
    categoriesLoading,
    error,
    refetch,

    // Filter state
    searchQuery,
    selectedTypeFilter,
    selectedCategoryFilter,

    // Filter options
    TYPE_FILTERS,
    CATEGORY_FILTERS,

    // Event handlers
    handleSearch,
    handleOpenAgent,
    handleTypeFilterChange,
    handleCategoryFilterChange,
  };
};

export type { Agent };
