import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router';
import { useAgentsApi, Agent } from 'src/services/api/use-agents-api';
import { useChatApi } from 'src/services/api/use-chat-api';
import { paths } from 'src/routes/paths';

// Filter options
const TYPE_FILTERS = ['All', 'SINGLE', 'TEAM'];
const CATEGORY_FILTERS = ['All', 'sales', 'marketing', 'social media'];

export const useAgentsView = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredAgents, setFilteredAgents] = useState<Agent[]>([]);
  const { useCreateAgents } = useAgentsApi();
  const { useCreateChat } = useChatApi();
  const navigate = useNavigate();
  const [isCreating, setIsCreating] = useState(false);
  const { mutate: createChat, isPending: isPendingCreateChat } = useCreateChat();
  // Filter states
  const [selectedTypeFilter, setSelectedTypeFilter] = useState(0);
  const [selectedCategoryFilter, setSelectedCategoryFilter] = useState(0);

  // Use the agents API hook to fetch data
  const { useGetAgentss } = useAgentsApi();
  const { data: agentsResponse, isLoading, error, refetch } = useGetAgentss();

  // Extract agents from the response
  const agents = agentsResponse?.agents || [];

  // Update filtered agents when agents data changes or filters change
  useEffect(() => {
    let filtered = agents;

    // Filter by type
    if (selectedTypeFilter !== 0) {
      const typeName = TYPE_FILTERS[selectedTypeFilter];
      filtered = filtered.filter((agent) => agent.template.type === typeName);
    }

    // Filter by category
    if (selectedCategoryFilter !== 0) {
      const categoryName = CATEGORY_FILTERS[selectedCategoryFilter];
      filtered = filtered.filter(
        (agent) => agent.template.category.name.toLowerCase() === categoryName.toLowerCase()
      );
    }

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(
        (agent) =>
          agent.template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          agent.template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
          agent.template.category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          agent.specialRequest.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredAgents(filtered);
  }, [agents, searchQuery, selectedTypeFilter, selectedCategoryFilter]);

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };
  const handleOpenAgent = async (obj: Agent) => {
    if (isCreating) return;

    setIsCreating(true);
    const body = {
      title: 'new chat',
      agentId: obj.id || '',
    };
    createChat(body, {
      onSuccess: (resChat) => {
        const newChatId = resChat?.data?.id;
        navigate(paths.dashboard.agents.chat(obj.template.id, obj.id, newChatId), {
          state: { name: obj.template.name },
        });
        setIsCreating(false);
      },
      onError: () => {
        setIsCreating(false);
      },
    });
  };
  // Handle filter changes
  const handleTypeFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedTypeFilter(newValue);
  };

  const handleCategoryFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedCategoryFilter(newValue);
  };

  return {
    // Data
    agents,
    filteredAgents,

    // Loading states
    isCreating,
    isLoading,
    error,
    refetch,

    // Filter state
    searchQuery,
    selectedTypeFilter,
    selectedCategoryFilter,

    // Filter options
    TYPE_FILTERS,
    CATEGORY_FILTERS,

    // Event handlers
    handleSearch,
    handleOpenAgent,
    handleTypeFilterChange,
    handleCategoryFilterChange,
  };
};

export type { Agent };
