import { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { Field } from 'src/components/hook-form/fields';

import { _account } from 'src/layouts/config-nav-account';
import { _notifications } from 'src/_mock/_notifications';
import { AccountMenu } from 'src/layouts/components/account-menu';
import { NotificationsMenu } from 'src/layouts/components/notifications-menu';
import { TeamMembersDialog } from 'src/sections/teams/chat/team-members-dialog';

import {
  Typography,
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  IconButton,
  useTheme,
  useMediaQuery,
  InputBase,
  Paper,
  Avatar,
  AvatarGroup,
  Button,
  TextField,
  alpha,
  Card,
  Tab,
  Tabs,
  Menu,
  MenuItem,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  InputAdornment,
} from '@mui/material';

// import { AppContainer } from 'src/components/common';
import { TeamChat } from 'src/sections/teams/chat/team-chat';
import { useTeamChat } from 'src/sections/teams/chat/use-team-chat';
import { Iconify } from 'src/components/iconify';
import { Logo } from 'src/components/logo';
import { AppButton } from 'src/components/common';
import { CONFIG } from 'src/config-global';
import AgentsChat from 'src/sections/agents/chat/agents-chat';

// ----------------------------------------------------------------------

export default function AgentsChatPage() {
  return (
    <>
      <Helmet>
        <title>Chat with agent Midad AI</title>
      </Helmet>
      <AgentsChat />
    </>
  );
}
