import { Alert, Box, Dialog, Paper, Typography } from '@mui/material';
import { StreamedMessage } from './use-agents-chat';

type ChatMessagesType = {
  chatMessages: StreamedMessage[];
  message?: string;
};
const ChatMessagesEvents = ({ chatMessages, message }: ChatMessagesType) => {
  console.log('message', message);
  return (
    <>
      {message && message?.length > 0 ? (
        <Alert sx={{ mt: '20px' }} variant="filled" color="error">
          <Typography sx={{ color: 'white' }}>{message}</Typography>
        </Alert>
      ) : (
        <></>
      )}

      {chatMessages.map((msg) => (
        <Box key={msg.id} display="flex" justifyContent="flex-start" mb={2} mt={3}>
          <Paper
            elevation={3}
            sx={{
              px: 2,
              py: 1.5,
              width: '100%',
              borderRadius: 3,

              border: '1px solid #E0DFE2',

              color: (theme) => theme.palette.text.primary,
            }}
          >
            <Typography variant="body2" fontWeight="bold" gutterBottom>
              {msg.source === 'user' ? 'You' : 'Agent'}
            </Typography>
            <Typography variant="body1" whiteSpace="pre-wrap">
              {msg.message}
            </Typography>
          </Paper>
        </Box>
      ))}
    </>
  );
};

export default ChatMessagesEvents;
