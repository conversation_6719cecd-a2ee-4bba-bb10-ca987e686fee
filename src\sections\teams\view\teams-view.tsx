import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  TextField,
  InputAdornment,
  Chip,
  Avatar,
  IconButton,
  Grid,
  CircularProgress,
  Alert,
  Divider,
  Menu,
  MenuItem,
} from '@mui/material';
import { Icon } from '@iconify/react';
import { useTheme } from '@mui/material/styles';
import { useRouter } from 'src/routes/hooks';
import { AppButton } from 'src/components/common';
import { useState } from 'react';
import { useTeamsView, TeamTemplate } from './use-teams-view';

const TeamsView = () => {
  const theme = useTheme();
  const router = useRouter();

  // Use the teams view hook to manage state and data
  const {
    filteredTeams,
    recentlyUsedTeams,
    isLoading,
    error,
    refetch,
    searchQuery,
    selectedTypeTab,
    selectedCategoryTab,
    TYPE_FILTERS,
    CATEGORY_FILTERS,
    handleSearch,
    handleTypeTabChange,
    handleCategoryTabChange,
  } = useTeamsView();

  const handleUseTemplate = () => {
    router.push('/dashboard/teams/create');
  };

  // Show loading state
  if (isLoading) {
    return (
      <Box
        sx={{
          width: '100%',
          minHeight: '100vh',
          bgcolor: 'background.default',
          p: { xs: 2, sm: 3, md: 4 },
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <CircularProgress size={40} />
      </Box>
    );
  }

  // Show error state
  if (error) {
    return (
      <Box
        sx={{
          width: '100%',
          minHeight: '100vh',
          bgcolor: 'background.default',
          p: { xs: 2, sm: 3, md: 4 },
        }}
      >
        <Alert severity="error" sx={{ mb: 3 }}>
          Failed to load team templates. Please try again.
        </Alert>
        <AppButton variant="outlined" onClick={() => refetch()} label="Retry" />
      </Box>
    );
  }

  const TeamCard = ({ team }: { team: TeamTemplate }) => {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);

    const handleMenuOpen = (event: React.MouseEvent<HTMLButtonElement>) => {
      setAnchorEl(event.currentTarget);
    };

    const handleMenuClose = () => {
      setAnchorEl(null);
    };

    const handleView = () => {
      console.log('View action triggered for:', team.id);
      handleMenuClose();
    };

    const handleDelete = () => {
      console.log('Delete action triggered for:', team.id);
      handleMenuClose();
    };

    return (
      <Card
        sx={{
          p: 3,
          height: '100%',
          border: '1px solid',
          borderColor: 'divider',
          background: (theme) =>
            `linear-gradient(145deg, ${theme.palette.background.paper} 0%, ${theme.palette.background.default} 100%)`,
          backdropFilter: 'blur(10px)',
          borderRadius: 3,
          position: 'relative',
          overflow: 'hidden',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          cursor: 'pointer',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '3px',
            background: (theme) =>
              `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 50%, ${theme.palette.error.main} 100%)`,
            opacity: 0,
            transition: 'opacity 0.3s ease',
          },
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: (theme) => `${theme.shadows[8]}, 0 0 0 1px ${theme.palette.primary.main}20`,
            borderColor: 'primary.main',
            '&::before': {
              opacity: 1,
            },
          },
        }}
      >
        <CardContent sx={{ p: 0, '&:last-child': { pb: 0 } }}>
          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
            <Box display="flex" alignItems="center" gap={1}>
              <Chip
                label={team.type}
                size="small"
                sx={{
                  bgcolor: (theme) => theme.palette.primary.light,
                  fontWeight: 600,
                  fontSize: '0.75rem',
                  letterSpacing: '0.025em',
                  border: (theme) => `1px solid ${theme.palette.primary.main}40`,
                  borderRadius: '6px',
                  height: '24px',
                  '& .MuiChip-label': {
                    px: 1.5,
                  },
                }}
              />
            </Box>
            <IconButton
              size="small"
              onClick={handleMenuOpen}
              sx={{
                color: 'text.secondary',
                borderRadius: '8px',
                transition: 'all 0.2s ease',
                '&:hover': {
                  bgcolor: (theme) => theme.palette.primary.main + '10',
                  color: 'primary.main',
                  transform: 'scale(1.05)',
                },
              }}
            >
              <Icon icon="eva:more-horizontal-fill" width={18} height={18} />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={open}
              onClose={handleMenuClose}
              anchorOrigin={{
                vertical: 'top',
                horizontal: 'left',
              }}
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
            >
              <MenuItem onClick={handleView}>View</MenuItem>
              <MenuItem onClick={handleDelete}>Delete</MenuItem>
            </Menu>
          </Box>

          {/* Model Information */}
          <Box display="flex" alignItems="center" gap={1} mb={2}>
            <Typography variant="body2" color="text.secondary" fontWeight={500}>
              Model:
            </Typography>
            <Chip
              label={team.model.replace(/_/g, ' ')}
              size="small"
              sx={{
                bgcolor: 'action.hover',
                color: 'inherit',
                fontSize: '0.7rem',
                height: '20px',
                '&:hover': {
                  backgroundColor: 'transparent',
                },
                '& .MuiChip-label': {
                  px: 1,
                },
              }}
            />
          </Box>

          <Typography variant="body2" color="text.secondary" sx={{ mb: 2, lineHeight: 1.4 }}>
            {team.description}
          </Typography>

          {/* Category Information */}
          <Box display="flex" alignItems="center" gap={1} mb={3}>
            <Avatar
              sx={{
                width: 44,
                height: 44,
                bgcolor: team.category.theme + '20',
                border: '1px solid',
                borderColor: team.category.theme + '40',
                transition: 'all 0.2s ease',
                '&:hover': {
                  transform: 'scale(1.1)',
                  bgcolor: team.category.theme + '30',
                  borderColor: team.category.theme,
                },
              }}
            >
              <Icon
                icon={team.category.icon}
                width={24}
                height={24}
                style={{ color: team.category.theme }}
              />
            </Avatar>
            <Box>
              <Typography
                variant="body2"
                fontWeight={600}
                color="text.primary"
                sx={{ textTransform: 'capitalize' }}
              >
                {team.category.name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {team.category.description}
              </Typography>
            </Box>
          </Box>

          <AppButton
            variant="outlined"
            color="primary"
            fullWidth
            onClick={handleUseTemplate}
            label=" Use Template"
          />
        </CardContent>
      </Card>
    );
  };

  return (
    <Box
      sx={{
        p: { xs: 2, sm: 3, md: 4 },
        bgcolor: 'background.default',
        minHeight: '100vh',
        position: 'relative',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: (theme) =>
            `radial-gradient(circle at 20% 20%, ${theme.palette.primary.main}10 0%, transparent 50%), radial-gradient(circle at 80% 80%, ${theme.palette.secondary.main}10 0%, transparent 50%)`,
          pointerEvents: 'none',
        },
      }}
    >
      {/* Header */}
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
        sx={{ position: 'relative', zIndex: 1 }}
      >
        <Box>
          <Typography variant="h3" fontWeight={800} color="text.primary">
            Teams
          </Typography>
          <Typography sx={{ color: 'rgba(15, 14, 17, 0.65)' }}>
            Enable advanced workflows with applications
          </Typography>
        </Box>
        <AppButton
          variant="contained"
          startIcon={<Icon icon="eva:plus-fill" width={20} height={20} />}
          fullWidth={false}
          label="Create Team"
        />
      </Box>
      <Divider sx={{ mb: 2 }} />

      {/* Recently Used Section */}
      <Box mb={6} sx={{ position: 'relative', zIndex: 1 }}>
        <Box mb={3}>
          <Typography
            variant="h5"
            fontWeight={700}
            color="text.primary"
            sx={{
              mb: 0.5,
              display: 'flex',
              alignItems: 'center',
              gap: 1,
            }}
          >
            <Icon
              icon="eva:clock-outline"
              width={24}
              height={24}
              color={theme.palette.primary.main}
            />
            Recently Used
          </Typography>
        </Box>
        <Grid container spacing={3}>
          {recentlyUsedTeams.map((template, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <TeamCard team={template} />
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* Teams Templates Section */}
      <Box sx={{ width: '100%', position: 'relative', zIndex: 1 }}>
        <Box mb={3}>
          <Typography
            variant="h5"
            fontWeight={700}
            color="text.primary"
            sx={{
              mb: 0.5,
              display: 'flex',
              alignItems: 'center',
              gap: 1,
            }}
          >
            <Icon
              icon="eva:grid-outline"
              width={24}
              height={24}
              color={theme.palette.primary.main}
            />
            Teams Templates
          </Typography>
        </Box>
        {/* Type Filters */}
        <Box display="flex" gap={1} mb={3}>
          {TYPE_FILTERS.map((type, index) => (
            <Chip
              key={type}
              label={type}
              onClick={() => handleTypeTabChange({} as React.SyntheticEvent, index)}
              sx={{
                borderRadius: '16px',
                width: '7%',
                bgcolor:
                  selectedTypeTab === index
                    ? (theme) => theme.palette.primary.main + '30'
                    : 'action.hover',
                color: selectedTypeTab === index ? theme.palette.secondary.main : 'inherit',
                borderColor: selectedTypeTab === index ? 'primary.main' : 'divider',
                fontWeight: selectedTypeTab === index ? 600 : 500,
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  color: theme.palette.primary.main,
                  bgcolor:
                    selectedTypeTab === index
                      ? (theme) => theme.palette.primary.main + '30'
                      : 'action.selected',
                  borderColor:
                    selectedTypeTab === index
                      ? 'primary.dark'
                      : (theme) => theme.palette.primary.main + '40',
                  transform: 'translateY(-1px)',
                  boxShadow: (theme) => theme.shadows[2],
                },
              }}
            />
          ))}
        </Box>
        {/* Search Bar */}
        <TextField
          placeholder="Search teams and templates..."
          fullWidth
          value={searchQuery}
          onChange={(e) => handleSearch(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Icon
                  icon="eva:search-fill"
                  color={theme.palette.text.secondary}
                  width={20}
                  height={20}
                />
              </InputAdornment>
            ),
          }}
          sx={{
            mb: 3,
            '& .MuiOutlinedInput-root': {
              bgcolor: 'divider',
              backdropFilter: 'blur(8px)',
              color: 'text.primary',
              borderRadius: 2,
              transition: 'all 0.2s ease-in-out',
              '& fieldset': {
                borderColor: 'divider',
                borderWidth: '1.5px',
              },
              '&:hover': {
                bgcolor: 'background.paper',
                '& fieldset': {
                  borderColor: (theme) => theme.palette.primary.main + '60',
                },
              },
              '&.Mui-focused': {
                bgcolor: 'background.paper',
                '& fieldset': {
                  borderColor: 'primary.main',
                  borderWidth: '2px',
                },
              },
            },
            '& .MuiInputBase-input': {
              color: 'text.primary',
              fontSize: '0.95rem',
              padding: '14px 16px',
              '&::placeholder': {
                color: 'text.secondary',
                opacity: 0.8,
              },
            },
          }}
        />

        {/* Category Filters */}
        <Box display="flex" gap={1} mb={3}>
          {CATEGORY_FILTERS.map((category, index) => (
            <Chip
              key={category}
              label={category}
              onClick={() => handleCategoryTabChange({} as React.SyntheticEvent, index)}
              sx={{
                borderRadius: '16px',
                width: '7%',
                bgcolor:
                  selectedCategoryTab === index
                    ? (theme) => theme.palette.primary.main + '30'
                    : 'action.hover',
                color: selectedCategoryTab === index ? theme.palette.secondary.main : 'inherit',
                borderColor: selectedCategoryTab === index ? 'primary.main' : 'divider',
                fontWeight: selectedCategoryTab === index ? 600 : 500,
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  color: theme.palette.primary.main,
                  bgcolor:
                    selectedCategoryTab === index
                      ? (theme) => theme.palette.primary.main + '30'
                      : 'action.selected',
                  borderColor:
                    selectedCategoryTab === index
                      ? 'primary.dark'
                      : (theme) => theme.palette.primary.main + '40',
                  transform: 'translateY(-1px)',
                  boxShadow: (theme) => theme.shadows[2],
                },
              }}
            />
          ))}
        </Box>

        {/* Templates Grid */}
        <Grid container spacing={3}>
          {filteredTeams.map((template, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <TeamCard team={template} />
            </Grid>
          ))}
        </Grid>

        {/* No Results */}
        {filteredTeams.length === 0 && (
          <Box
            sx={{
              textAlign: 'center',
              py: 12,
              px: 4,
            }}
          >
            <Box
              sx={{
                width: 120,
                height: 120,
                mx: 'auto',
                mb: 3,
                borderRadius: '50%',
                bgcolor: 'action.hover',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Icon
                icon="eva:search-outline"
                width={48}
                height={48}
                style={{ color: theme.palette.text.disabled }}
              />
            </Box>
            <Typography variant="h5" color="text.primary" sx={{ mb: 2, fontWeight: 600 }}>
              No team templates found
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 400, mx: 'auto' }}>
              Try adjusting your search or filter criteria to find the team templates you&apos;re
              looking for
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default TeamsView;
