import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';
import { CategoryFormValues } from 'src/sections/categories/form/category-schema';

// Define the API endpoints for categories
export const categoryEndpoints = {
  list: '/categories',
  details: '/categories',
};

// Define the Category data type
export interface Category {
  id: string;
  name: string;
  description: string;
  icon: string;
  colorType: 'primary' | 'secondary' | 'success' | 'warning' | 'info' | 'error' | 'custom';
  customColor?: string;
  agentsCount: number;
}

// Create a hook to use the categories API
export const useCategoriesApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all categories
  const useGetCategories = () => {
    return apiServices.useGetListService<Category[]>({
      url: categoryEndpoints.list,
    });
  };

  // Get a single category by ID
  const useGetCategory = (id: string) => {
    return apiServices.useGetItemService<Category>({
      url: categoryEndpoints.details,
      id,
    });
  };

  return {
    useGetCategories,
    useGetCategory,
  };
};
